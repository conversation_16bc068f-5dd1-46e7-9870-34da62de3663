/*
  李长江: 10.14.242.177:9114
  何雪峰: 10.14.242.166:9114
  冯春杰: 10.14.243.139:8014
  何杰:10.14.242.192:9114
*/
// const CONFIG = {
//   sourceServiceAPI: "http://10.14.242.166:9114",
//   flowServiceAPI: "http://10.242.98.119:8017",
//   masterServiceAPI: "http://23mk369v.dnat.tech", // 冯春杰
//   ruleServiceAPI: " http://10.14.242.192:9114",
// };
// module.exports = {
//   "/api/workFlow": {
//     target: CONFIG.flowServiceAPI,
//     changeOrigin: true,
//     pathRewrite: {
//       "^/api/workFlow": "",
//     },
//   },
//   "/api/masterDataManagement": {
//     target: CONFIG.masterServiceAPI,
//     changeOrigin: true,
//     pathRewrite: {
//       "^/api/masterDataManagement": "",
//     },
//   },
//   "/api/sourcing": {
//     target: CONFIG.sourceServiceAPI,
//     changeOrigin: true,
//     pathRewrite: {
//       "^/api/sourcing": "",
//     },
//   },
//   "/api/ruleConfig": {
//     target: CONFIG.ruleServiceAPI,
//     changeOrigin: true,
//     pathRewrite: {
//       "^/api/ruleConfig": "",
//     },
//   },
// };

module.exports = {
  '/api': {
    // target: 'http://srm-uat-gw.eads.tcl.com',
    // target: 'https://srm-sit-main.eads.tcl.com',
    // target: 'https://srm-uat-main.eads.tcl.com',
    target: 'http://srm-sit-gw.eads.tcl.com',
    changeOrigin: true,
    pathRewrite: {
      // "^/api/": "",
    }
  },
  '/mock-api': {
    target: 'http://localhost:8081',
    changeOrigin: true,
    pathRewrite: {
      '^/mock-api': ''
    }
  }
}
