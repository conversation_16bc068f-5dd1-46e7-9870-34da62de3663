<template>
  <div :class="['top-info', !isExpand && 'top-info-small']">
    <!-- {{headerInfo}} -->
    <!-- statusCreate判断是不是创建页 -->
    <!-- 头部的内容 -->
    <div class="header-box">
      <!-- 左侧的信息 -->
      <div class="header-box-left" v-if="!headerInfo.statusCreate || headerInfo.status == 0">
        <div :class="[statusToClass(headerInfo.status), 'mr20']">
          {{ headerInfo.status | statusFormat }}
        </div>
        <div class="infos mr20">{{ $t('VMI领料单号：') }}{{ headerInfo.vmiOrderCode }}</div>
        <div class="infos mr20">{{ $t('制单人：') }}{{ headerInfo.createUserName }}</div>
        <div class="infos">
          {{ $t('制单时间：') }}{{ headerInfo.createTime | dateFormat }}
          {{ headerInfo.createTime | timeFormat }}
        </div>
      </div>

      <div class="middle-blank"></div>

      <!-- 右侧操作按钮 -->
      <mt-button css-class="e-flat" :is-primary="true" @click="goBack">{{ $t('返回') }}</mt-button>
      <!-- 领料-采方-保存、提交 -->
      <mt-button
        v-if="!headerInfo.statusCreate"
        css-class="e-flat"
        :is-primary="true"
        @click="saveBtn"
        >{{ $t('保存') }}</mt-button
      >
      <mt-button
        css-class="e-flat"
        v-if="!headerInfo.statusCreate"
        :is-primary="true"
        @click="submitBtn"
        >{{ $t('提交') }}</mt-button
      >
      <div class="sort-box" @click="doExpand">
        <mt-icon
          v-for="index in 2"
          :key="index"
          :name="isExpand ? 'icon_Sort_up' : 'icon_Sort_down'"
        ></mt-icon>
      </div>
    </div>

    <!-- 下面的内容 -->
    <div class="main-bottom mt20">
      <mt-form ref="ruleForm" :model="headerInfo" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <!-- :disabled="headerInfo.statusCreate" -->
          <mt-select
            ref="factoryRef"
            v-model="headerInfo.siteCode"
            :data-source="factoryOptions"
            :disabled="true"
            :show-clear-button="true"
            :allow-filtering="true"
            :filtering="getFactoryOptions"
            :fields="{ text: 'labelShow', value: 'siteCode' }"
            :placeholder="$t('工厂')"
            @change="handleFactoryChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('钢材供应商')">
          <mt-select
            ref="supplierCodeRef"
            v-model="headerInfo.supplierCode"
            :data-source="supplierOptions"
            :disabled="true"
            :show-clear-button="true"
            :allow-filtering="true"
            :filtering="getSupplierOptions"
            :fields="{ text: 'labelShow', value: 'supplierCode' }"
            :placeholder="$t('请选择')"
            @change="handleSupplierChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓')">
          <mt-input
            ref="vmiWarehouseCodeRef"
            v-model="headerInfo.vmiWarehouseName"
            :disabled="true"
          ></mt-input>
        </mt-form-item>

        <mt-form-item prop="outsourcedType" :label="$t('委外方式')">
          <mt-select
            v-model="headerInfo.outsourcedType"
            :data-source="outsourcedTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :placeholder="$t('请选择委外方式')"
            :show-clear-button="true"
            :disabled="headerInfo.statusCreate"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="processorCode" :label="$t('钣金供应商')">
          <mt-select
            v-model="headerInfo.processorCode"
            :data-source="processorOptions"
            :disabled="headerInfo.statusCreate"
            :show-clear-button="true"
            :allow-filtering="true"
            :filtering="getProcessorOptions"
            :fields="{ text: 'labelShow', value: 'processorCode' }"
            :placeholder="$t('请选择')"
            @change="handleProcessorChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('送货地址')">
          <mt-select
            v-model="headerInfo.addressCode"
            v-if="!headerInfo.statusCreate"
            :data-source="WarehouseOptions"
            :disabled="headerInfo.statusCreate"
            :show-clear-button="true"
            :popup-width="600"
            :allow-filtering="true"
            :fields="{
              text: 'label',
              value: 'consigneeAddressCode'
            }"
            :placeholder="$t('请选择')"
            @change="vmiWarehouseAddressChange"
          ></mt-select>
          <mt-input
            v-model="headerInfo.vmiWarehouseAddress"
            v-else
            :disabled="headerInfo.statusCreate"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="full-width" prop="remark" :label="$t('备注')" :show-message="false">
          <mt-input
            v-model="headerInfo.remark"
            @change="remarkChange"
            :disabled="headerInfo.statusCreate"
            :placeholder="$t('备注')"
          ></mt-input>
        </mt-form-item>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <!-- 财务共享组织 -->
            <mt-form-item style="width: 100%" :label="$t('财务共享组织')" prop="orgCode">
              <mt-input v-model="headerInfo.orgCode" placeholder="" :disabled="true" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <!-- 所辖公司编码 -->
            <mt-form-item style="width: 100%" :label="$t('财务共享公司编码')" prop="companyCode">
              <mt-input v-model="headerInfo.companyCode" placeholder="" :disabled="true" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              style="width: 100%"
              prop="supplierAmount"
              :label="$t('应付账款')"
              v-if="!headerInfo.statusCreate"
            >
              <mt-input
                ref="supplierAmount"
                v-model="headerData.supplierAmount"
                :disabled="true"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              style="width: 100%"
              prop="customerAmount"
              :label="$t('应收账款')"
              v-if="!headerInfo.statusCreate"
            >
              <mt-input
                ref="customerAmount"
                v-model="headerData.customerAmount"
                :disabled="true"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item
              style="width: 100%"
              prop="creditAmount"
              :label="$t('倒挂额度')"
              v-if="!headerInfo.statusCreate"
            >
              <mt-input
                ref="creditAmount"
                v-model="headerData.creditAmount"
                :disabled="true"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              style="width: 100%"
              prop="transitAmount"
              :label="$t('其他在途单据送货金额')"
              v-if="!headerInfo.statusCreate"
            >
              <mt-input
                ref="transitAmount"
                v-model="headerData.transitAmount"
                :disabled="true"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              style="width: 100%"
              prop="availablePickupAmount"
              :label="$t('剩余可调料金额')"
              v-if="!headerInfo.statusCreate"
            >
              <mt-input
                ref="availablePickupAmount"
                v-model="headerData.availablePickupAmount"
                :disabled="true"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item
              style="width: 100%"
              prop="currentPickupTotalAmount"
              :label="$t('本次调料金额')"
              v-if="!headerInfo.statusCreate"
            >
              <mt-input
                ref="currentPickupTotalAmount"
                v-model="headerData.currentPickupTotalAmount"
                :disabled="true"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row></mt-row>
      </mt-form>
    </div>
  </div>
</template>

<script>
import { StatusText, StatusCssClass, DeliveryTypeOptions } from '../config/constant'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { utils } from '@mtech-common/utils'
import { cloneDeep } from 'lodash'

export default {
  props: {
    headerInfo1: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const columnList = JSON.parse(sessionStorage.getItem('createDate'))
    return {
      utils,
      remark: '',
      headerInfo: '',
      DeliveryTypeOptions,
      isExpand: true,
      columnList,
      orgCode: '',
      outsourcedTypeOptions: [
        { text: this.$t('标准委外'), value: 0 },
        { text: this.$t('销售委外'), value: 1 },
        { text: this.$t('标准采购'), value: 2 },
        { text: this.$t('工序委外'), value: 3 }
      ],
      headerData: {
        supplierAmount: null,
        customerAmount: null,
        creditAmount: null,
        currentPickupTotalAmount: null,
        transitAmount: null,
        availablePickupAmount: null
      },
      rules: {
        siteCode: [{ required: true, message: this.$t('请选择工厂'), trigger: 'blur' }],
        processorCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择原材料供应商'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseCode: [{ required: true, message: this.$t('请选择VMI仓'), trigger: 'blur' }],
        outsourcedType: [{ required: true, message: this.$t('请选择委外方式'), trigger: 'blur' }],
        vmiWarehouseAddress: [
          {
            required: true,
            message: this.$t('请选择领料供应商'),
            trigger: 'blur'
          }
        ]
      },
      factoryOptions: [], // 工厂下拉数据
      supplierOptions: [], // 原材料供应商下拉数据
      warehouseOptions: [], // VMI仓下拉数据
      processorOptions: [], // 领料供应商下拉数据
      WarehouseOptions: [],
      selectVal: this.initVal, // initVal 也时刻被改变着
      dataList: [],
      dataLimit: 20 // 限制返回条数
    }
  },
  watch: {
    'headerInfo.siteCode': {
      handler(val) {
        if (val) {
          this.getOrgCodeList(val)
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // if (!this.headerInfo.statusCreate) {
    //   this.headerInfo.processorCode = "";
    // }
    this.headerInfo = cloneDeep(this.headerInfo1)
    if (this.headerInfo.statusCreate) {
      // this.rules = {

      // vmiWarehouseAddress: [
      //   {
      //     required: true,
      //     message: this.$t("请输入送货地址"),
      //     trigger: "blur",
      //   },
      // ],
      // };

      // 获取工厂下拉数据（列出默认的前20条）
      this.getFactoryOptions()
      // 获取（原材料）供应商下拉数据（列出默认的前20条）
      this.getSupplierOptions()
      // 如果工厂和（原材料）供应商已选，则获取VMI仓下拉数据（列出默认的前20条）
      if (this.headerInfo.siteCode != '' && this.headerInfo.supplierCode != '') {
        // 获取VMI仓下拉数据
        this.getVMIOptions()
      }

      // 引入mtech-common/utils中的防抖，(mtech-common/utils )
      this.getSelectOptions = utils.debounce(this.getSelectOptions, 300)
    }
    // 获取领料供应商下拉数据（列出默认的前20条）
    this.getProcessorOptions()
    // this.getWarehouse();
    // 如果有初始值
    if (this.headerInfo.siteCode && this.headerInfo.siteCode != '') {
      // 处理数据-回显工厂
      this.processingResourceData('site', [
        {
          siteCode: this.headerInfo.siteCode,
          siteName: this.headerInfo.siteName
        }
      ])
    }
    if (this.headerInfo.supplierCode && this.headerInfo.supplierCode != '') {
      // 处理数据-回显（原材料）供应商
      this.processingResourceData('supplier', [
        {
          supplierCode: this.headerInfo.supplierCode,
          supplierName: this.headerInfo.supplierName
        }
      ])
    }
    if (this.headerInfo.vmiWarehouseCode && this.headerInfo.vmiWarehouseCode != '') {
      // 处理数据-回显VMI仓
      this.processingResourceData('warehouse', [
        {
          vmiWarehouseCode: this.headerInfo.vmiWarehouseCode,
          vmiWarehouseName: this.headerInfo.vmiWarehouseName
        }
      ])
    }
    if (this.headerInfo.processorCode && this.headerInfo.processorCode != '') {
      // 处理数据-回显领料供应商
      this.processingResourceData('processor', [
        {
          supplierCode: this.headerInfo.processorCode,
          supplierName: this.headerInfo.processorName
        }
      ])
    }
  },
  filters: {
    dateFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
      } else {
        str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
      }

      return str
    },
    timeFormat(value) {
      let str = ''
      if (isNaN(Number(value))) {
        str = timeStringToDate({ formatString: 'HH:MM:SS', value })
      } else {
        str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
      }

      return str
    },
    statusFormat(value) {
      if (!StatusText[value]) {
        return value
      } else {
        return StatusText[value]
      }
    }
  },
  methods: {
    // 获取共享组织列表
    getOrgCodeList(val) {
      this.$API.outsourcingNew.getSiteCodeToOrgCode({ siteCode: val }).then((res) => {
        if (res && res.code === 200) {
          this.$set(this.headerInfo, 'orgCode', res.data.orgCode + ' - ' + res.data.orgName)
          this.orgCode = res.data?.orgCode
          this.$set(
            this.headerInfo,
            'companyCode',
            res.data.companyList.map((item) => item.companyCode).join()
          )
        }
      })
    },
    getAmt(code) {
      if (!this.orgCode || !code) {
        return
      }
      this.$API.outsourcingNew
        .getSiteCodeToOrgCodeAmt({
          orgCode: this.orgCode,
          supplierCode: code
        })
        .then((res) => {
          if (res && res.code === 200) {
            this.headerData.creditAmount = res.data?.amount || 0
          }
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg || '获取倒挂金额失败')
          })
        })
    },
    // 获取账单
    getAmmount(isEdit, newItemList) {
      if (isEdit) {
        let params = {
          // dataLimit: this.dataLimit,
          siteCode: this.headerInfo.siteCode,
          supplierCode: this.headerInfo.supplierCode,
          processorCode: this.headerInfo.processorCode,
          itemList: newItemList
        }
        this.$API.purchaseCoordination.getAmmountData(params).then((res) => {
          this.headerData = res.data
          for (let i = 0; i < res.data.itemList.length; i++) {
            this.headerInfo.itemList[i].currentPickupAmount =
              res.data.itemList[i].currentPickupAmount
          }
          this.$emit('updateItemList', this.headerInfo.itemList)
        })
      } else if (this.headerInfo.itemList) {
        let params = {
          // dataLimit: this.dataLimit,
          siteCode: this.headerInfo.siteCode,
          supplierCode: this.headerInfo.supplierCode,
          processorCode: this.headerInfo.processorCode,
          itemList: this.headerInfo.itemList
        }
        this.$API.purchaseCoordination.getAmmountData(params).then((res) => {
          this.headerData = res.data
          for (let i = 0; i < res.data.itemList.length; i++) {
            this.headerInfo.itemList[i].currentPickupAmount =
              res.data.itemList[i].currentPickupAmount
          }
          this.$emit('updateItemList', this.headerInfo.itemList)
        })
      } else {
        let params = {
          // dataLimit: this.dataLimit,
          siteCode: this.headerInfo.siteCode,
          supplierCode: this.headerInfo.supplierCode,
          processorCode: this.headerInfo.processorCode,
          itemList: this.columnList
        }
        this.$API.purchaseCoordination.getAmmountData(params).then((res) => {
          this.headerData = res.data
          for (let i = 0; i < res.data.itemList.length; i++) {
            this.columnList[i].currentPickupAmount = res.data.itemList[i].currentPickupAmount
          }
          this.$emit('updateItemList', this.columnList)
        })
      }
    },
    // 状态 转对应的 css class
    statusToClass(value) {
      let cssClass = ''
      if (StatusCssClass[value]) {
        cssClass = StatusCssClass[value]
      }
      return cssClass
    },
    // 查询工厂下拉数据（模糊搜索）
    getFactoryOptions(e = { text: '' }) {
      // 获取工厂下拉数据
      this.getSelectOptions(e, 'site')
    },
    // 查询（原材料）供应商下拉数据（模糊搜索）
    getSupplierOptions(e = { text: '' }) {
      // 获取（原材料）供应商下拉数据
      this.getSelectOptions(e, 'supplier')
    },
    // 查询VMI仓下拉数据（传入工厂和原材料供应商）（模糊搜索）
    getVMIOptions(e = { text: '' }) {
      // 获取VMI仓下拉数据（传入工厂和原材料供应商）
      this.getSelectOptions(e, 'warehouse', {
        siteCode: this.headerInfo.siteCode,
        supplierCode: this.headerInfo.supplierCode
      })
    },
    remarkChange(e) {
      this.headerInfo.remark = e
      sessionStorage.setItem('createDate', JSON.stringify(this.headerInfo))
    },
    // 获取领料供应商下拉数据（领料供应商是来自于供应商主数据的接口）（模糊搜索）
    getProcessorOptions(e = { text: '' }) {
      // 获取领料供应商下拉数据
      this.getSelectOptions(e, 'processor')
    },
    getWarehouse() {
      let obj = {
        // dataLimit: this.dataLimit,
        siteCode: this.headerInfo.siteCode,
        supplierCode: this.headerInfo.processorCode
      }
      this.$API.purchaseCoordination.listConfigByParam(obj).then((res) => {
        this.WarehouseOptions = res.data
      })
    },
    // 收货单头上的工厂等那些信息的筛选按照林健说的逻辑稍微改下，接口还是之前的接口，我加了个参数：resultType，
    // 可选值为：site(工厂)/warehouse(VMI仓库)/supplier(供应商);
    // 你需要查工厂的时候就传site，需要查仓库就传warehouse，需要查供应商就传supplier；
    // 查VMI仓要根据工厂过滤就将选择的工厂编码作为查询VMI仓的条件一起传进去
    // 采方-VMI领料单详情==查询工厂、查询（原材料）供应商、查询VMI仓（传入工厂和原材料供应商）、查询领料供应商（领料供应商是来自于供应商主数据的接口）下拉数据
    getSelectOptions(e = { text: '' }, resultType, params) {
      // dataLimit 限制返回条数
      let obj = {
        // dataLimit: this.dataLimit,
        fuzzyNameOrCode: e.text
      }
      if (resultType == 'processor') {
        // 查询领料供应商下拉数据（领料供应商是来自于供应商主数据的接口）
        this.$API.masterData.getSupplier(obj).then((res) => {
          this.filteringResource(e, resultType, res)
        })
      } else {
        obj['resultType'] = resultType
        if (params) {
          obj = { ...obj, ...params }
        }
        this.$API.purchaseCoordination.VMIQueryCriteriaByResultType(obj).then((res) => {
          this.filteringResource(e, resultType, res)
        })
      }
    },
    // 处理 模糊搜索 筛选 的数据
    filteringResource(e, resultType, res) {
      if (res.code === 200) {
        let resData = res.data || []

        // 处理数据
        this.processingResourceData(resultType, resData)

        if (res.total > this.dataLimit) {
          this.$toast({
            content: this.$t('搜索结果较多，请再输入更精确的查询条件'),
            type: 'warning'
          })
        }
      }
    },
    // 处理数据
    processingResourceData(resultType, resData) {
      if (resultType === 'site') {
        // 工厂
        // this.factoryOptions = resData;
        this.factoryOptions = resData.map((item) => {
          item.labelShow = item.siteCode + ' - ' + item.siteName
          return item
        })
      } else if (resultType === 'supplier') {
        // （原材料）供应商
        // this.supplierOptions = resData;
        // 如果有 拼接 code-名称，放到 labelShow 字段里
        this.supplierOptions = resData.map((item) => {
          item.labelShow = item.supplierCode + ' - ' + item.supplierName
          return item
        })
      } else if (resultType === 'warehouse') {
        // VMI仓库
        // this.warehouseOptions = resData;
        // 如果有 拼接 code-名称，放到 labelShow 字段里
        this.warehouseOptions = resData.map((item) => {
          item.labelShow = item.vmiWarehouseCode + ' - ' + item.vmiWarehouseName
          return item
        })
      } else if (resultType === 'processor') {
        // 领料供应商
        // this.processorOptions = resData;
        // 如果有 拼接 code-名称，放到 labelShow 字段里
        this.processorOptions = resData.map((item) => {
          item.processorCode = item.supplierCode
          item.processorName = item.supplierName
          item.labelShow = item.supplierCode + ' - ' + item.supplierName
          delete item.supplierCode
          delete item.supplierName
          return item
        })
      }
    },
    handleChange(e) {
      console.log('handleChange', e)
      // TODO: 如果清空选中值
      // if (!e.value && e.value != 0) {
      //   this.getDatalist({ text: "" }); // 否则列出默认的前20条
      // } else {
      //   this.$emit("update:initVal", e.value);
      // }
    },
    // 工厂切换时
    handleFactoryChange(val) {
      this.headerInfo.siteCode = val.itemData.siteCode
      this.headerInfo.siteName = val.itemData.siteName
      // 清空VMI仓，让重新选择
      // this.headerInfo.vmiWarehouseCode = "";
      // this.headerInfo.vmiWarehouseName = "";
      // 如果工厂和（原材料）供应商已选，则获取VMI仓下拉数据
      if (this.headerInfo.siteCode != '' && this.headerInfo.supplierCode != '') {
        // 获取VMI仓下拉数据
        this.getSelectOptions({ text: '' }, 'warehouse', {
          siteCode: this.headerInfo.siteCode,
          supplierCode: this.headerInfo.supplierCode
        })
      }
      // 根据供应商与工厂查出送货地址 可修改
      // if (this.headerInfo.processorCode != "") {
      //   let obj = {
      //     supplierCode: this.headerInfo.processorCode,
      //     siteCode: this.headerInfo.siteCode,
      //   };
      // this.getWarehouseAddress(obj);
      // }
    },
    // 原材料供应商切换
    handleSupplierChange(val) {
      this.headerInfo.supplierCode = val.itemData.supplierCode
      this.headerInfo.supplierName = val.itemData.supplierName
      // 清空VMI仓，让重新选择
      // this.headerInfo.vmiWarehouseCode = "";
      // this.headerInfo.vmiWarehouseName = "";
      // 如果工厂和（原材料）供应商已选，则获取VMI仓下拉数据
      if (this.headerInfo.siteCode != '' && this.headerInfo.supplierCode != '') {
        // 获取VMI仓下拉数据
        this.getSelectOptions({ text: '' }, 'warehouse', {
          siteCode: this.headerInfo.siteCode,
          supplierCode: this.headerInfo.supplierCode
        })
      }
    },

    // 根据VMI仓查出送货地址 可修改
    getWarehouseAddress(vmiWarehouseCode) {
      this.$API.purchaseCoordination.siteTenantExtendQuery(vmiWarehouseCode).then((res) => {
        res.data.forEach((item) => {
          item.label = item.consigneeName + item.consigneePhone + `-` + item.consigneeAddress
        })
        this.WarehouseOptions = res.data
        // if (res.code === 200 && res.data) {
        //   this.headerInfo.vmiWarehouseAddress =
        //     res.data[0].consigneeAddress || "";
        //   this.headerInfo.consigneeAddressCode =
        //     res.data[0].consigneeAddressCode;
        //   this.headerInfo.consigneePhone = res.data[0].consigneePhone;
        //   this.headerInfo.consigneeName = res.data[0].consigneeName;
        //   this.headerInfo.addressId = res.data[0].id;
        // }
      })
    },
    vmiWarehouseAddressChange(val) {
      // this.headerInfo.vmiWarehouseAddress = val.itemData.consigneeAddress;
      this.headerInfo.vmiWarehouseAddress = val.itemData.label

      this.headerInfo.consigneePhone = val.itemData.consigneePhone
      this.headerInfo.consigneeName = val.itemData.consigneeName

      sessionStorage.setItem('createDate', JSON.stringify(this.headerInfo))
    },
    // 领料供应商切换
    handleProcessorChange(val) {
      this.headerInfo.processorCode = val.itemData.processorCode
      this.headerInfo.processorName = val.itemData.processorName
      sessionStorage.setItem('createDate', JSON.stringify(this.headerInfo))
      // 根据供应商与工厂查出送货地址 可修改
      if (this.headerInfo.siteCode != '') {
        let obj = {
          supplierCode: val.itemData.processorCode,
          siteCode: this.headerInfo.siteCode
        }
        this.getWarehouseAddress(obj)
      }
      this.getAmmount()
      this.getAmt(val.itemData.processorCode)
    },
    // 返回
    goBack() {
      this.$emit('goBack')
    },
    // 保存
    saveBtn() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('saveBtn', this.headerInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 提交
    submitBtn() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('submitBtn', this.headerInfo)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 展开按钮
    doExpand() {
      this.isExpand = !this.isExpand
      this.$emit('doExpand')
    }
  }
}
</script>

<style lang="scss" scoped>
.mr20 {
  margin-right: 20px;
}
.top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-box-left {
      height: 50px;
      display: flex;
      align-items: center;
    }

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      position: relative;
      cursor: pointer;
      margin-right: 20px;
      .mt-icons {
        font-size: 12px;
        transform: scale(0.5);
        color: rgba(0, 70, 156, 1);
        margin-top: -10px;
        position: absolute;
        top: 0;

        &:nth-child(2) {
          top: 6px;
        }
      }
    }
  }

  .main-bottom {
    /deep/ .mt-form-item {
      width: calc((100% - 20px - 280px - 20px * 2) / 3);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      &.two-thirds-full-width {
        width: calc(((100% - 20px - 280px - 20px) / 3) * 2) !important;
      }
      &.full-width {
        width: calc(100% - 20px - 280px) !important;
      }
    }
  }
}
</style>
