<template>
  <div>
    <!-- 物料编码使用原有的弹框选择方式 -->
    <div v-if="data.column.field === 'itemCode'" class="in-cell">
      <debounce-filter-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :request="postChange"
        :data-source="dataSource"
        :fields="{ text: 'codeAndName', value: 'itemCode' }"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
      ></debounce-filter-select>
      <mt-icon style="width: 20px" name="icon_input_search" @click.native="showDialog"></mt-icon>
    </div>

    <!-- 采购订单号和采购订单行号使用纯下拉选择 -->
    <div v-else>
      <mt-select
        :id="data.column.field"
        v-model="data[data.column.field]"
        :data-source="dataSource"
        :fields="{ text: 'codeAndName', value: 'value' }"
        :placeholder="placeholder"
        @change="selectChange"
        :open-dispatch-change="true"
        :disabled="isDisabled"
        :allow-filtering="true"
        :filtering="postChange"
      ></mt-select>
    </div>

    <mt-dialog
      ref="dialog"
      css-class="pc-item-dialog"
      :header="title"
      :buttons="buttons"
      @close="handleClose"
    >
      <div class="full-height">
        <mt-template-page
          ref="templateRef"
          class="template-height has-page"
          :hidden-tabs="true"
          :template-config="pageConfig"
          @recordDoubleClick="recordDoubleClick"
        ></mt-template-page>
      </div>
    </mt-dialog>
  </div>
</template>
<script>
import { PROXY_MDM_AUTH } from '@/utils/constant'
// import { utils } from "@mtech-common/utils";
import { maxPageSize } from '@/utils/constant'

export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      title: this.$t('请选择'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          toolbar: [],
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          gridId: '3aee5144-1201-4eb5-8a0d-8b5792261a9a',
          grid: {
            // height: 352,
            // allowPaging: true,
            allowSelection: true,
            selectionSettings: {
              checkboxOnly: false
            },
            columnData: [
              {
                width: '150',
                field: 'itemCode',
                headerText: this.$t('物料编号')
              },
              {
                width: '150',
                field: 'itemName',
                headerText: this.$t('物料名称')
              }
              // {
              //   width: "150",
              //   field: "itemUnitDescription",
              //   headerText: this.$t("单位"),
              // },
              // {
              //   width: "150",
              //   field: "purchaseGroupName",
              //   headerText: this.$t("采购组"),
              // },
              // {
              //   width: "150",
              //   field: "batchCode",
              //   headerText: this.$t("批次号"),
              // },
            ],
            asyncConfig: {},
            dataSource: []
          }
        }
      ],
      data: {},
      placeholder: this.$t('请选择'),
      fields: { text: 'label', value: 'value' },
      dataSource: [],
      purchase: '', //采购组code
      isDisabled: false,
      codeArr: JSON.parse(sessionStorage.getItem('codeArr')),
      sapComponentData: [] // 存储SAP组件明细数据，用于采购订单行号联动
    }
  },
  mounted() {
    this.codeArr = JSON.parse(sessionStorage.getItem('codeArr') || '{}')

    if (this.data.column.field === 'itemCode') {
      // 优化物料编码初始化逻辑
      const currentItemCode = this.data.itemCode
      console.log('物料编码字段初始化，当前值:', currentItemCode)

      if (currentItemCode) {
        // 如果有初始值，先获取包含该物料编码的数据
        this.getCategoryItem(currentItemCode)

        // 同时确保当前值在数据源中（处理编辑模式下的回显）
        this.$nextTick(() => {
          this.ensureCurrentItemInDataSource(currentItemCode)
        })
      } else {
        // 如果没有初始值，获取默认数据
        this.getCategoryItem('')
      }
    }
    if (this.data.column.field === 'warehouseCode') {
      this.getWarehouseCodeOptions()
    }

    // 根据字段类型初始化数据源
    if (this.data.column.field === 'orderCode') {
      console.log('采购订单号字段初始化，物料编码:', this.data.itemCode)

      // 主动检查同行的物料编码
      this.checkAndUpdateItemCodeFromRow()

      if (this.data.itemCode) {
        console.log('有物料编码，调用getOrderCodeOptions方法')
        this.getOrderCodeOptions()
      } else {
        console.log('没有物料编码，等待物料编码选择后再获取采购订单号数据')
      }
    } else if (this.data.column.field === 'orderItemNo') {
      console.log(
        '采购订单行号字段初始化，物料编码:',
        this.data.itemCode,
        '采购订单号:',
        this.data.orderCode
      )

      // 直接获取采购订单行号选项
      this.getorderItemNoOptions()
    }

    // 监听物料编码变化事件
    this.$bus.$on('itemCodeChange', this.handleItemCodeChange)
    // 监听物料编码变化专门用于采购订单号字段的事件
    this.$bus.$on('itemCodeChangeForOrderCode', this.handleItemCodeChangeForOrderCode)
    // 监听其他字段请求物料编码的事件
    this.$bus.$on('requestItemCodeForRow', this.handleRequestItemCodeForRow)
    // 监听物料编码响应事件
    this.$bus.$on('responseItemCodeForRow', this.handleResponseItemCodeForRow)
    // 监听采购订单号变化事件
    this.$bus.$on('orderCodeChange', this.handleOrderCodeChange)
    // 监听父组件的采购订单号选择事件
    this.$parent.$on('orderCodeSelected', this.handleOrderCodeSelected)
    // 监听全局采购订单号选择事件
    this.$bus.$on('orderCodeSelectedGlobal', this.handleOrderCodeSelectedGlobal)
  },
  methods: {
    recordDoubleClick(args) {
      this.selectChange({ itemData: args.rowData })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm(_, records) {
      console.log('=== confirm方法开始执行 ===')
      console.log('传入的records参数:', records)

      // 如果没有传入records或records为空，尝试从表格获取选中记录
      if (!records || records.length <= 0) {
        console.log('未传入records，尝试从表格获取选中记录')

        // 增强数据获取的健壮性，逐步检查每个环节
        const templateRef = this.$refs.templateRef
        console.log('templateRef:', templateRef)

        if (!templateRef) {
          console.error('templateRef不存在')
          this.$toast({
            content: this.$t('获取表格引用失败'),
            type: 'warning'
          })
          return
        }

        const currentUsefulRef = templateRef.getCurrentUsefulRef()
        console.log('currentUsefulRef:', currentUsefulRef)

        if (!currentUsefulRef) {
          console.error('currentUsefulRef不存在')
          this.$toast({
            content: this.$t('获取表格实例失败'),
            type: 'warning'
          })
          return
        }

        const ejsRef = currentUsefulRef.ejsRef
        console.log('ejsRef:', ejsRef)

        if (!ejsRef) {
          console.error('ejsRef不存在')
          this.$toast({
            content: this.$t('获取表格组件失败'),
            type: 'warning'
          })
          return
        }

        // 尝试获取选中记录
        try {
          records = ejsRef.getSelectedRecords()
          console.log('从表格获取的选中记录:', records)
        } catch (error) {
          console.error('获取选中记录时发生错误:', error)
          this.$toast({
            content: this.$t('获取选中记录失败'),
            type: 'warning'
          })
          return
        }
      }

      // 检查是否有选中记录
      if (!records || records.length <= 0) {
        console.warn('没有选中任何记录')
        this.$toast({
          content: this.$t('请先选择一条记录'),
          type: 'warning'
        })
        return
      }

      console.log('最终使用的records:', records)
      console.log('选中的第一条记录:', records[0])

      // 调用selectChange处理选中数据
      this.selectChange({ itemData: records[0] })

      // 确保弹框选择后的数据能正确回显
      this.$nextTick(() => {
        console.log('confirm方法执行完成后强制更新组件')
        this.$forceUpdate()
      })
    },
    showDialog() {
      this.pageConfig[0].grid.asyncConfig = {
        url: `${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        recordsPosition: 'data.records',
        params: {
          customerEnterpriseId: this.codeArr.buyerEnterpriseId,
          organizationCode: this.codeArr.siteCode
        }
      }
      this.$refs.dialog.ejsRef.show()
    },
    // 获取库存地点
    getWarehouseCodeOptions() {
      let obj = {
        enterpriseId: this.codeArr.buyerEnterpriseId,
        params: {
          page: {
            size: maxPageSize,
            current: 1
          },
          condition: 'and',
          defaultRules: [
            {
              // 工厂
              field: 'siteCode',
              operator: 'equal',
              value: this.codeArr.siteCode
            }
          ]
        }
      }
      this.$API.receiptAndDelivery.postSiteTenantExtendQueryByEnterpriseId(obj).then((res) => {
        res.data.records.forEach((item) => {
          item.name = item.siteAddressName //
          item.value = item.siteAddress //
        })
        this.dataSource = res.data.records.map((i) => {
          return {
            ...i,
            codeAndName: `${i.siteAddress} - ${i.siteAddressName}`
          }
        })
      })
    },
    // 模糊搜索 - 适配mt-select的filtering事件
    postChange(e) {
      if (this.data.column.field === 'itemCode') {
        this.getCategoryItem(e.text)
      } else if (this.data.column.field === 'orderCode') {
        // 对于采购订单号，进行本地过滤
        if (e.text) {
          const filteredData = this.dataSource.filter(
            (item) => item.codeAndName && item.codeAndName.includes(e.text)
          )
          e.updateData(filteredData)
        } else {
          e.updateData(this.dataSource)
        }
      } else if (this.data.column.field === 'orderItemNo') {
        // 对于采购订单行号，进行本地过滤
        if (e.text) {
          const filteredData = this.dataSource.filter(
            (item) => item.codeAndName && item.codeAndName.includes(e.text)
          )
          e.updateData(filteredData)
        } else {
          e.updateData(this.dataSource)
        }
      }
    },

    getCategoryItem(e) {
      //物料下拉
      let obj = {
        page: {
          current: 1,
          size: 20
        },
        rules: [
          {
            field: 'itemCode',
            operator: 'likeright',
            value: e
          }
        ],
        customerEnterpriseId: this.codeArr.buyerEnterpriseId,
        organizationCode: this.codeArr.siteCode
      }
      this.$API.masterData.getOrgRel(obj).then((res) => {
        console.log('物料编码接口返回原始数据:', res.data.records)

        this.dataSource =
          res.data.records.map((i) => {
            const item = {
              ...i,
              codeAndName: `${i.itemCode} - ${i.itemName}`,
              itemCode: i.itemCode, // 作为value字段
              name: i.itemCode,
              value: i.itemCode,
              text: `${i.itemCode} - ${i.itemName}`
            }
            return item
          }) || []

        console.log('处理后的物料编码数据源:', this.dataSource)
        console.log('数据源第一项结构:', this.dataSource[0])
      })
    },

    // 确保选中的物料编码在下拉选项中存在
    ensureItemInDataSource(itemData) {
      console.log('=== ensureItemInDataSource方法开始执行 ===')
      console.log('检查物料是否在数据源中:', itemData)

      if (!itemData || !itemData.itemCode) {
        console.warn('itemData或itemCode为空，跳过处理')
        return
      }

      // 检查当前数据源中是否已存在该物料编码
      const existingItem = this.dataSource.find((item) => item.itemCode === itemData.itemCode)

      if (existingItem) {
        console.log('物料编码已存在于数据源中:', itemData.itemCode)
        return
      }

      console.log('物料编码不在数据源中，添加到数据源:', itemData.itemCode)

      // 构造符合数据源格式的物料项
      const newItem = {
        ...itemData,
        codeAndName: `${itemData.itemCode} - ${itemData.itemName || ''}`,
        itemCode: itemData.itemCode,
        name: itemData.itemCode,
        value: itemData.itemCode,
        text: `${itemData.itemCode} - ${itemData.itemName || ''}`
      }

      // 将新物料添加到数据源的开头，确保它能被正确显示
      this.dataSource.unshift(newItem)

      console.log('物料编码已添加到数据源，新的数据源长度:', this.dataSource.length)
      console.log('添加的物料项:', newItem)
      console.log('更新后的完整数据源:', this.dataSource)

      // 立即强制更新组件以确保下拉选项正确显示（同步更新，不使用nextTick）
      console.log('立即强制更新下拉组件以同步显示选中项')
      this.$forceUpdate()
    },

    // 确保当前物料编码值在数据源中存在（用于编辑模式回显）
    ensureCurrentItemInDataSource(itemCode) {
      console.log('=== ensureCurrentItemInDataSource方法开始执行 ===')
      console.log('检查当前物料编码是否在数据源中:', itemCode)

      if (!itemCode) {
        console.warn('itemCode为空，跳过处理')
        return
      }

      // 检查当前数据源中是否已存在该物料编码
      const existingItem = this.dataSource.find((item) => item.itemCode === itemCode)

      if (existingItem) {
        console.log('当前物料编码已存在于数据源中:', itemCode)
        return
      }

      console.log('当前物料编码不在数据源中，需要获取详细信息并添加:', itemCode)

      // 调用接口获取该物料的详细信息
      const obj = {
        page: {
          current: 1,
          size: 1
        },
        rules: [
          {
            field: 'itemCode',
            operator: 'equal',
            value: itemCode
          }
        ],
        customerEnterpriseId: this.codeArr.buyerEnterpriseId,
        organizationCode: this.codeArr.siteCode
      }

      this.$API.masterData
        .getOrgRel(obj)
        .then((res) => {
          console.log('获取当前物料详细信息返回:', res.data.records)

          if (res.data.records && res.data.records.length > 0) {
            const itemData = res.data.records[0]

            // 构造符合数据源格式的物料项
            const newItem = {
              ...itemData,
              codeAndName: `${itemData.itemCode} - ${itemData.itemName}`,
              itemCode: itemData.itemCode,
              name: itemData.itemCode,
              value: itemData.itemCode,
              text: `${itemData.itemCode} - ${itemData.itemName}`
            }

            // 将物料添加到数据源的开头
            this.dataSource.unshift(newItem)

            console.log('当前物料编码已添加到数据源:', newItem)

            // 强制更新组件
            this.$nextTick(() => {
              this.$forceUpdate()
            })
          } else {
            console.warn('未找到当前物料编码的详细信息:', itemCode)
          }
        })
        .catch((error) => {
          console.error('获取当前物料详细信息失败:', error)
        })
    },

    // 获取采购订单号下拉数据
    getOrderCodeOptions(searchText = '') {
      console.log('=== getOrderCodeOptions方法开始执行 ===')
      console.log('itemCode:', this.data.itemCode)
      console.log('searchText:', searchText)
      console.log('调用条件检查: 有itemCode:', !!this.data.itemCode)

      // 不管是否为销售委外，都应该调用获取采购订单号接口
      if (this.data.itemCode) {
        console.log('✅ 有物料编码，开始获取SAP组件数据')
        // 首先尝试从已有数据或sessionStorage获取SAP组件数据
        let sapData = this.sapComponentData
        if (!sapData || sapData.length === 0) {
          const storageKey = `sapComponentData_${this.data.itemCode}_${this.codeArr.siteCode}_${this.codeArr.supplierCode}`
          const storedData = sessionStorage.getItem(storageKey)
          if (storedData) {
            try {
              sapData = JSON.parse(storedData)
              this.sapComponentData = sapData
              console.log('从sessionStorage获取SAP组件数据用于采购订单号:', sapData)
            } catch (e) {
              console.error('解析sessionStorage中的SAP组件数据失败:', e)
            }
          }
        }

        // 如果已有SAP数据，直接使用
        if (sapData && sapData.length > 0) {
          console.log('使用已有SAP数据生成采购订单号选项')
          this.generateOrderCodeOptions(sapData, searchText)
          return
        }
        // 如果没有SAP数据，则调用接口获取
        const params = {
          siteCode: this.codeArr.siteCode,
          supplierCode: this.codeArr.supplierCode,
          itemCode: this.data.itemCode
        }

        console.log('调用getSapComponentDetails接口，参数:', params)

        this.$API.outsourcing
          .getSapComponentDetails(params)
          .then((res) => {
            console.log('getSapComponentDetails接口返回:', res)

            // 存储完整的SAP组件数据，用于采购订单行号联动
            this.sapComponentData = res.data || []

            // 将SAP组件数据存储到sessionStorage，供其他组件实例使用
            const storageKey = `sapComponentData_${this.data.itemCode}_${this.codeArr.siteCode}_${this.codeArr.supplierCode}`
            sessionStorage.setItem(storageKey, JSON.stringify(this.sapComponentData))
            console.log('SAP组件数据已存储到sessionStorage，key:', storageKey)

            // 生成采购订单号选项
            this.generateOrderCodeOptions(this.sapComponentData, searchText)
          })
          .catch((error) => {
            console.error('getSapComponentDetails接口调用失败:', error)
            this.dataSource = []
          })
      } else {
        console.log('❌ 没有物料编码，清空dataSource')
        console.log('当前itemCode值:', this.data.itemCode)
        this.dataSource = []
      }
    },

    // 生成采购订单号选项
    generateOrderCodeOptions(sapData, searchText = '', callback = null) {
      console.log('生成采购订单号选项，SAP数据:', sapData)

      if (!sapData || sapData.length === 0) {
        console.log('没有SAP数据，清空采购订单号选项')
        this.dataSource = []
        // 通知调用方没有查询到采购订单号
        if (callback && typeof callback === 'function') {
          callback(false)
        }
        return false
      }

      // 处理返回的数据，提取唯一的采购订单号
      let uniqueOrderCodes = [...new Set(sapData.map((item) => item.orderCode).filter(Boolean))]

      console.log('提取的唯一采购订单号:', uniqueOrderCodes)

      // 如果有搜索文本，进行过滤
      if (searchText) {
        uniqueOrderCodes = uniqueOrderCodes.filter((orderCode) => orderCode.includes(searchText))
        console.log('过滤后的采购订单号:', uniqueOrderCodes)
      }

      const hasOrderCodes = uniqueOrderCodes.length > 0

      this.dataSource = uniqueOrderCodes.map((orderCode) => ({
        orderCode,
        value: orderCode,
        codeAndName: orderCode,
        text: orderCode
      }))

      console.log('设置的采购订单号dataSource:', this.dataSource)
      console.log('dataSource第一项结构:', this.dataSource[0])
      console.log('当前字段值:', this.data[this.data.column.field])
      console.log('是否有采购订单号:', hasOrderCodes)

      // 解决时序问题：在数据源设置后，重新设置字段值以触发组件更新
      this.$nextTick(() => {
        const currentValue = this.data[this.data.column.field]
        if (currentValue) {
          console.log('重新设置字段值以解决时序问题:', currentValue)
          // 临时清空值
          this.data[this.data.column.field] = ''
          // 在下一个tick重新设置值
          this.$nextTick(() => {
            this.data[this.data.column.field] = currentValue
            console.log('字段值重新设置完成:', this.data[this.data.column.field])
          })
        }
      })

      // 通知调用方查询结果
      if (callback && typeof callback === 'function') {
        callback(hasOrderCodes)
      }

      return hasOrderCodes
    },

    // 获取采购订单行号下拉数据 - 从已有数据中筛选，不调用接口
    getorderItemNoOptions(searchText = '') {
      console.log('获取采购订单行号，当前采购订单号:', this.data.orderCode)
      console.log('当前物料编码:', this.data.itemCode)

      // 尝试从sessionStorage获取SAP组件数据
      let sapData = this.sapComponentData
      if (!sapData || sapData.length === 0) {
        const storageKey = `sapComponentData_${this.data.itemCode}_${this.codeArr.siteCode}_${this.codeArr.supplierCode}`
        const storedData = sessionStorage.getItem(storageKey)
        if (storedData) {
          try {
            sapData = JSON.parse(storedData)
            this.sapComponentData = sapData
            console.log('从sessionStorage获取SAP组件数据:', sapData)
          } catch (e) {
            console.error('解析sessionStorage中的SAP组件数据失败:', e)
            sapData = []
          }
        }
      }

      console.log('使用的SAP组件数据:', sapData)

      // 检查是否有采购订单号和SAP组件数据
      if (this.data.orderCode && sapData && sapData.length > 0) {
        // 过滤出当前选择的采购订单号对应的行号
        let orderItems = sapData.filter((item) => item.orderCode === this.data.orderCode)

        console.log('过滤出的订单项:', orderItems)

        // 如果有搜索文本，进行过滤
        if (searchText) {
          orderItems = orderItems.filter(
            (item) => item.orderItemNo && item.orderItemNo.includes(searchText)
          )
        }

        // 提取唯一的订单行号
        const uniqueorderItemNos = [
          ...new Set(orderItems.map((item) => item.orderItemNo).filter(Boolean))
        ]

        console.log('提取的唯一订单行号:', uniqueorderItemNos)

        this.dataSource = uniqueorderItemNos.map((orderItemNo) => ({
          orderItemNo,
          value: orderItemNo,
          codeAndName: orderItemNo,
          text: orderItemNo
        }))

        console.log('采购订单行号dataSource:', this.dataSource)
        console.log('采购订单行号第一项结构:', this.dataSource[0])
        console.log('当前采购订单行号值:', this.data[this.data.column.field])

        // 解决时序问题：在数据源设置后，重新设置字段值以触发组件更新
        this.$nextTick(() => {
          const currentValue = this.data[this.data.column.field]
          if (currentValue) {
            console.log('重新设置采购订单行号值以解决时序问题:', currentValue)
            // 临时清空值
            this.data[this.data.column.field] = ''
            // 在下一个tick重新设置值
            this.$nextTick(() => {
              this.data[this.data.column.field] = currentValue
              console.log('采购订单行号值重新设置完成:', this.data[this.data.column.field])
            })
          }
        })
      } else {
        console.log('清空采购订单行号数据源')
        console.log(
          '原因 - 采购订单号:',
          this.data.orderCode,
          'SAP数据长度:',
          sapData ? sapData.length : 0
        )
        this.dataSource = []
      }
    },
    selectChange(val) {
      console.log('=== selectChange方法开始执行 ===')
      console.log('传入的val参数:', val)
      console.log('val.itemData:', val.itemData)
      console.log('当前字段:', this.data.column.field)

      if (this.data.column.field === 'itemCode') {
        // 处理物料编码选择
        console.log('处理物料编码选择')
        console.log('val对象的属性:', Object.keys(val))
        console.log('val.value:', val.value)
        console.log('val.itemData:', val.itemData)

        // 增强数据获取逻辑，支持多种数据结构
        let itemCode = null
        let itemData = null

        // 优先从val.itemData获取数据（弹框选择的情况）
        if (val.itemData) {
          itemData = val.itemData
          itemCode = itemData.itemCode || itemData.value || val.value
          console.log('从itemData获取物料编码:', itemCode)
        }
        // 其次从val.value获取（下拉选择的情况）
        else if (val.value) {
          itemCode = val.value
          console.log('从value获取物料编码:', itemCode)
        }
        // 最后尝试直接从val获取（兼容其他情况）
        else if (val.itemCode) {
          itemCode = val.itemCode
          console.log('直接从val获取物料编码:', itemCode)
        }

        console.log('最终确定的物料编码:', itemCode)

        // 检查是否是相同的物料编码（避免双击编辑时不必要的清空）
        const currentItemCode = this.data[this.data.column.field]
        // 判断是否为真正的相同选择：
        // 1. 当前有值且不为空字符串
        // 2. 新值与当前值完全相同
        // 3. 且当前已有采购订单号（说明是编辑状态而非新增状态）
        const hasCurrentValue = currentItemCode && currentItemCode.trim() !== ''
        const hasOrderCode = this.data.orderCode && this.data.orderCode.trim() !== ''
        const isSameItemCode = hasCurrentValue && currentItemCode === itemCode && hasOrderCode
        console.log('当前物料编码:', currentItemCode)
        console.log('新选择的物料编码:', itemCode)
        console.log('当前是否有值:', hasCurrentValue)
        console.log('当前是否有采购订单号:', hasOrderCode)
        console.log('是否为相同物料编码选择:', isSameItemCode)

        if (itemCode) {
          console.log('开始更新物料编码到表单')

          // 如果有itemData，先确保选中的物料编码在下拉选项中存在（关键：必须在赋值前处理）
          if (itemData) {
            console.log('先确保选中的物料编码在下拉选项中存在:', itemData)
            this.ensureItemInDataSource(itemData)
          }

          // 使用$set确保响应式更新
          this.$set(this.data, this.data.column.field, itemCode)

          // 如果有itemData，同时更新物料名称等相关字段
          if (itemData) {
            console.log('同时更新相关字段，itemData:', itemData)

            // 更新物料名称（如果存在）
            if (itemData.itemName && Object.prototype.hasOwnProperty.call(this.data, 'itemName')) {
              this.$set(this.data, 'itemName', itemData.itemName)
              console.log('更新物料名称:', itemData.itemName)
            }

            // 更新其他可能的字段
            if (
              itemData.itemUnitDescription &&
              Object.prototype.hasOwnProperty.call(this.data, 'itemUnitDescription')
            ) {
              this.$set(this.data, 'itemUnitDescription', itemData.itemUnitDescription)
              console.log('更新单位:', itemData.itemUnitDescription)
            }
          }

          // 只有当物料编码发生变化时才清空采购订单号和采购订单行号
          if (!isSameItemCode) {
            console.log('物料编码发生变化，清空采购订单号和采购订单行号')
            this.$set(this.data, 'orderCode', '')
            this.$set(this.data, 'orderItemNo', '')
          } else {
            console.log('物料编码未变化，保持采购订单号和采购订单行号不变')
          }

          console.log('更新后的data[itemCode]:', this.data[this.data.column.field])
          console.log('更新后的完整data:', this.data)

          // 强制触发表单更新和组件重新渲染
          this.$nextTick(() => {
            console.log('强制触发表单更新')
            // 触发父组件的数据变更事件
            this.$parent.$emit('dataChanged', {
              field: this.data.column.field,
              value: itemCode,
              rowData: this.data
            })

            // 强制更新组件以确保下拉选项正确回显
            this.$forceUpdate()
            console.log('已强制更新组件，确保弹框选择后正确回显')
          })
        } else {
          console.warn('未能获取到有效的物料编码')
          this.$toast({
            content: this.$t('获取物料编码失败，请重新选择'),
            type: 'warning'
          })
          return
        }

        // 只有当物料编码发生变化时才调用相关接口和触发事件
        if (itemCode && !isSameItemCode) {
          console.log('物料编码发生变化，调用相关接口和触发事件')

          let obj = {
            buyerEnterpriseId: this.codeArr.buyerEnterpriseId,
            createType: 2,
            itemCode: itemCode,
            buyerOrgCode: this.codeArr.buyerOrgCode,
            isOutSale: this.codeArr.isOutSale,
            id: this.data?.id,
            siteCode: this.codeArr.siteCode,
            supplierCode: this.codeArr.supplierCode
          }

          console.log('调用outNewWaitQuerySapOutDemand接口，参数:', obj)

          this.$API.outsourcing
            .outNewWaitQuerySapOutDemand(obj)
            .then((res) => {
              console.log('outNewWaitQuerySapOutDemand接口返回:', res)
              if (res.data && res.data.length > 0) {
                this.$bus.$emit('itemNameChange', res.data[0].itemName) //传给物料名称
                this.$bus.$emit('planGroupNameChange', res.data[0].planGroupName) // 计划组
                this.$bus.$emit('buyerOrgNameChange', {
                  buyerGroupName: res.data[0].buyerGroupName,
                  buyerGroupCode: res.data[0].buyerGroupCode
                }) // 采购组

                this.$bus.$emit('basicUnitCodeChange', {
                  basicUnitCode: res.data[0].basicUnitCode,
                  basicUnitName: res.data[0].basicUnitName
                }) //传给单位
                this.$bus.$emit('maxDemandQuantityChange', res.data[0].maxReceiveQuantity) //传给可调拨数量
                this.$bus.$emit('supplierStockChange', res.data[0].supplierStock) //传给库存现有
              }
            })
            .catch((error) => {
              console.error('outNewWaitQuerySapOutDemand接口调用失败:', error)
            })

          // 触发采购订单号数据更新（不管是否为销售委外）
          console.log('触发采购订单号数据更新')
          this.$bus.$emit('itemCodeChange', {
            itemCode: itemCode,
            rowIndex: this.data.addId || this.data.id
          })

          // 额外通知采购订单号字段直接更新数据，并根据查询结果决定是否清空
          this.$nextTick(() => {
            console.log('=== 通知采购订单号字段更新数据 ===')
            const eventData = {
              itemCode: itemCode,
              rowIndex: this.data.addId || this.data.id,
              // 添加回调函数，用于处理查询结果
              callback: (hasOrderCodes) => {
                console.log('采购订单号查询结果:', hasOrderCodes)
                if (!hasOrderCodes) {
                  console.log('没有查询到采购订单号，清空相关字段')
                  this.$set(this.data, 'orderCode', '')
                  this.$set(this.data, 'orderItemNo', '')
                } else {
                  console.log('查询到采购订单号，保持现有值')
                }
              }
            }
            console.log('发送事件数据:', eventData)
            console.log('当前行索引:', this.data.addId || this.data.id)
            this.$bus.$emit('itemCodeChangeForOrderCode', eventData)
            console.log('itemCodeChangeForOrderCode事件已发送')
          })
        } else if (itemCode && isSameItemCode) {
          console.log('物料编码未变化，跳过接口调用和事件触发')
        }

        // 无论是否有物料编码，都要关闭弹框
        this.handleClose()

        // 在关闭弹框后强制更新组件以确保回显
        this.$nextTick(() => {
          console.log('弹框关闭后强制更新组件以确保回显')
          this.$forceUpdate()
        })
      }

      // 采购订单号选择处理
      if (this.data.column.field === 'orderCode') {
        console.log('处理采购订单号选择')

        // 增强数据获取逻辑
        let orderCode = null
        if (val.itemData) {
          orderCode = val.itemData.orderCode || val.itemData.value || val.itemData.codeAndName
        } else if (val.value) {
          orderCode = val.value
        } else if (val.orderCode) {
          orderCode = val.orderCode
        }

        console.log('最终确定的采购订单号:', orderCode)

        if (orderCode) {
          // 使用$set确保响应式更新
          this.$set(this.data, 'orderCode', orderCode)
          console.log('采购订单号更新成功:', orderCode)

          // 清空采购订单行号
          this.$set(this.data, 'orderItemNo', '')
          console.log('已清空采购订单行号')
        } else {
          console.warn('未能获取到有效的采购订单号')
          this.$toast({
            content: this.$t('获取采购订单号失败，请重新选择'),
            type: 'warning'
          })
          return
        }

        // 触发采购订单行号数据更新（通过事件通知其他行的采购订单行号组件）
        this.$bus.$emit('orderCodeChange', {
          orderCode: orderCode,
          rowIndex: this.data.addId || this.data.id,
          itemCode: this.data.itemCode // 添加物料编码信息
        })

        // 通过父组件事件通知同一行的采购订单行号组件
        this.$nextTick(() => {
          this.$parent.$emit('orderCodeSelected', {
            orderCode: orderCode,
            rowIndex: this.data.addId || this.data.id,
            itemCode: this.data.itemCode
          })
        })

        // 额外通过全局事件确保所有相关组件都能收到通知
        this.$nextTick(() => {
          this.$bus.$emit('orderCodeSelectedGlobal', {
            orderCode: orderCode,
            rowIndex: this.data.addId || this.data.id,
            itemCode: this.data.itemCode
          })
        })
      }

      // 采购订单行号选择处理
      if (this.data.column.field === 'orderItemNo') {
        console.log('处理采购订单行号选择')

        // 增强数据获取逻辑
        let orderItemNo = null
        if (val.itemData) {
          orderItemNo = val.itemData.orderItemNo || val.itemData.value || val.itemData.codeAndName
        } else if (val.value) {
          orderItemNo = val.value
        } else if (val.orderItemNo) {
          orderItemNo = val.orderItemNo
        }

        console.log('最终确定的采购订单行号:', orderItemNo)

        if (orderItemNo) {
          // 使用$set确保响应式更新
          this.$set(this.data, 'orderItemNo', orderItemNo)
          console.log('采购订单行号更新成功:', orderItemNo)

          // 强制触发表单更新
          this.$nextTick(() => {
            console.log('强制触发采购订单行号表单更新')
            // 触发父组件的数据变更事件
            this.$parent.$emit('dataChanged', {
              field: this.data.column.field,
              value: orderItemNo,
              rowData: this.data
            })
          })
        } else {
          console.warn('未能获取到有效的采购订单行号')
          this.$toast({
            content: this.$t('获取采购订单行号失败，请重新选择'),
            type: 'warning'
          })
          return
        }
      }

      if (this.data.column.field === 'warehouseCode') {
        console.log('处理库存地点选择')

        // 增强数据获取逻辑
        let warehouseCode = null
        let warehouseName = null

        if (val.itemData) {
          warehouseCode =
            val.itemData.siteAddress || val.itemData.value || val.itemData.warehouseCode
          warehouseName =
            val.itemData.siteAddressName || val.itemData.name || val.itemData.warehouseName
        } else if (val.value) {
          warehouseCode = val.value
          warehouseName = val.text || val.name
        }

        console.log('最终确定的库存地点编码:', warehouseCode)
        console.log('最终确定的库存地点名称:', warehouseName)

        if (warehouseCode) {
          // 使用$set确保响应式更新
          this.$set(this.data, 'warehouseCode', warehouseCode)
          if (warehouseName) {
            this.$set(this.data, 'warehouseName', warehouseName)
          }

          console.log('库存地点更新成功')

          // 触发父组件事件
          this.$parent.$emit('selectedChanged', {
            //传出额外数据
            fieldCode: 'warehouseCode',
            itemInfo: {
              ...this.data
            }
          })

          // 关闭弹框
          this.handleClose()
        } else {
          console.warn('未能获取到有效的库存地点')
          this.$toast({
            content: this.$t('获取库存地点失败，请重新选择'),
            type: 'warning'
          })
          return
        }
      }
    },

    // 主动检查并更新同行的物料编码
    checkAndUpdateItemCodeFromRow() {
      console.log('检查同行物料编码，当前字段:', this.data.column.field)

      // 通过父组件获取同行的物料编码
      if (this.$parent && this.$parent.getRowData) {
        try {
          const rowData = this.$parent.getRowData(this.data.addId || this.data.id)
          if (rowData && rowData.itemCode && !this.data.itemCode) {
            console.log('从同行获取物料编码:', rowData.itemCode)
            this.data.itemCode = rowData.itemCode
          }
        } catch (error) {
          console.log('无法通过父组件获取行数据，尝试事件方式')
        }
      }

      // 如果父组件方法不可用，尝试通过事件获取
      if (!this.data.itemCode) {
        this.$bus.$emit('requestItemCodeForRow', {
          rowIndex: this.data.addId || this.data.id,
          requestField: this.data.column.field
        })
      }
    },

    // 处理物料编码变化事件
    handleItemCodeChange(data) {
      console.log('收到物料编码变化事件:', data)
      console.log('当前组件行索引:', this.data.addId || this.data.id)
      console.log('当前字段:', this.data.column.field)

      if (data.rowIndex === (this.data.addId || this.data.id)) {
        console.log('匹配到当前行，开始处理物料编码变化')

        // 如果当前字段就是物料编码，只更新数据但不重新获取选项，避免干扰当前的选择操作
        if (this.data.column.field === 'itemCode') {
          console.log('当前字段是物料编码，只更新数据不重新获取选项')
          // 更新物料编码
          this.data.itemCode = data.itemCode
          return
        }

        // 更新物料编码
        this.data.itemCode = data.itemCode

        // 清空当前行的采购订单号和采购订单行号
        this.data.orderCode = ''
        this.data.orderItemNo = ''

        // 清空数据源并重新获取数据
        this.dataSource = []

        // 根据当前字段类型重新获取数据
        if (this.data.column.field === 'orderCode') {
          console.log('当前是采购订单号字段，重新获取数据')
          // 延迟调用，确保物料编码已经更新
          this.$nextTick(() => {
            this.getOrderCodeOptions()
          })
        } else if (this.data.column.field === 'orderItemNo') {
          console.log('当前是采购订单行号字段，清空数据源')
          // 采购订单行号字段也需要清空
          this.dataSource = []
        }
      }
    },

    // 处理其他字段请求物料编码的事件
    handleRequestItemCodeForRow(data) {
      // 只有物料编码字段且是同一行才响应
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'itemCode' &&
        this.data.itemCode
      ) {
        console.log('物料编码字段响应请求，发送物料编码:', this.data.itemCode)

        // 直接发送物料编码给请求的字段
        this.$bus.$emit('responseItemCodeForRow', {
          rowIndex: this.data.addId || this.data.id,
          itemCode: this.data.itemCode,
          targetField: data.requestField
        })
      }
    },

    // 处理物料编码响应事件
    handleResponseItemCodeForRow(data) {
      // 只有目标字段且是同一行才处理
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        data.targetField === this.data.column.field &&
        data.itemCode
      ) {
        console.log('收到物料编码响应，更新并获取数据:', data.itemCode)

        // 更新物料编码
        this.data.itemCode = data.itemCode

        // 如果是采购订单号字段，重新获取数据
        if (this.data.column.field === 'orderCode') {
          this.getOrderCodeOptions()
        }
      }
    },

    // 处理物料编码变化专门用于采购订单号字段的事件
    handleItemCodeChangeForOrderCode(data) {
      console.log('=== 收到物料编码变化专用事件（采购订单号字段） ===')
      console.log('事件数据:', data)
      console.log('当前组件行索引:', this.data.addId || this.data.id)
      console.log('当前字段:', this.data.column.field)
      console.log('行索引匹配:', data.rowIndex === (this.data.addId || this.data.id))
      console.log('字段匹配:', this.data.column.field === 'orderCode')

      // 只有采购订单号字段才处理这个事件
      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'orderCode'
      ) {
        console.log('✅ 匹配到当前行的采购订单号字段，开始更新数据')

        // 更新物料编码
        this.data.itemCode = data.itemCode

        // 清空当前采购订单号
        this.data.orderCode = ''

        // 清空数据源并重新获取数据
        this.dataSource = []

        // 重新获取采购订单号选项
        this.$nextTick(() => {
          console.log('重新获取采购订单号选项')
          this.getOrderCodeOptions()
        })
      }
    },

    // 处理采购订单号变化事件
    handleOrderCodeChange(data) {
      console.log('收到采购订单号变化事件:', data)
      console.log('当前组件行索引:', this.data.addId || this.data.id)
      console.log('当前字段:', this.data.column.field)

      if (data.rowIndex === (this.data.addId || this.data.id)) {
        // 更新当前行的采购订单号
        this.data.orderCode = data.orderCode

        if (this.data.column.field === 'orderItemNo') {
          // 如果当前是采购订单行号字段，清空并重新获取数据
          console.log('当前是采购订单行号字段，重新获取数据')
          this.data.orderItemNo = ''
          this.dataSource = []

          // 重新获取采购订单行号数据
          this.getorderItemNoOptions()
        }
      }
    },

    // 处理父组件的采购订单号选择事件
    handleOrderCodeSelected(data) {
      console.log('收到父组件采购订单号选择事件:', data)

      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'orderItemNo'
      ) {
        console.log('当前是采购订单行号字段，更新数据源')

        // 更新采购订单号
        this.data.orderCode = data.orderCode
        // 清空采购订单行号
        this.data.orderItemNo = ''
        // 重新获取采购订单行号数据
        this.getorderItemNoOptions()
      }
    },

    // 处理全局采购订单号选择事件
    handleOrderCodeSelectedGlobal(data) {
      console.log('收到全局采购订单号选择事件:', data)

      if (
        data.rowIndex === (this.data.addId || this.data.id) &&
        this.data.column.field === 'orderItemNo' &&
        data.itemCode === this.data.itemCode
      ) {
        console.log('全局事件匹配，当前是采购订单行号字段，更新数据源')

        // 更新采购订单号
        this.data.orderCode = data.orderCode
        // 清空采购订单行号
        this.data.orderItemNo = ''
        // 重新获取采购订单行号数据
        this.getorderItemNoOptions()
      }
    }
  },
  deactivated() {
    // 清理事件监听
    this.$bus.$off('maxDemandQuantityChange')
    this.$bus.$off('warehouseChange')
    this.$bus.$off('itemNameChange')
    this.$bus.$off('warehouseChange2')
    this.$bus.$off('itemCodeChange', this.handleItemCodeChange)
    this.$bus.$off('itemCodeChangeForOrderCode', this.handleItemCodeChangeForOrderCode)
    this.$bus.$off('requestItemCodeForRow', this.handleRequestItemCodeForRow)
    this.$bus.$off('responseItemCodeForRow', this.handleResponseItemCodeForRow)
    this.$bus.$off('orderCodeChange', this.handleOrderCodeChange)
    this.$parent.$off('orderCodeSelected', this.handleOrderCodeSelected)
    this.$bus.$off('orderCodeSelectedGlobal', this.handleOrderCodeSelectedGlobal)

    // 清理组件数据
    this.codeArr = null
    this.dataSource = []
    this.sapComponentData = []

    console.log('Select组件已清理数据')
  },

  beforeDestroy() {
    // 在组件销毁前也进行清理，确保内存释放
    this.codeArr = null
    this.dataSource = []
    this.sapComponentData = []

    console.log('Select组件销毁前清理完成')
  }
}
</script>

<style scoped lang="scss">
.in-cell {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  /deep/ .mt-select {
    .e-input-group-icon,
    .e-ddl-icon,
    .e-search-icon {
      margin-right: 20px;
    }
  }
  > .mt-icons {
    margin-left: 5px;
    cursor: pointer;
    position: absolute;
    top: 8px;
    right: 0;
  }
}
</style>
